import { useState } from "react";
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  PDFDownloadLink,
  PDFViewer,
  Font,
} from "@react-pdf/renderer";

// Register fonts
Font.register({
  family: "Roboto",
  src: "https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-light-webfont.ttf",
});

// Register Thai font - Noto Sans Thai
Font.register({
  family: "Noto Sans Thai",
  fonts: [
    {
      src: "https://fonts.gstatic.com/s/notosansthai/v20/iJWnBXeUZi_OHPqn4wq6hQ2_hbJ1xyN9wd43SofNWcd1MKVQt_So_9CdU5RtpzF-QRvzzXg.woff2",
      fontWeight: 400,
    },
    {
      src: "https://fonts.gstatic.com/s/notosansthai/v20/iJWnBXeUZi_OHPqn4wq6hQ2_hbJ1xyN9wd43SofNWcd1MKVQt_So_9CdU5RtpzF-QRvzzXg.woff2",
      fontWeight: 500,
    },
    {
      src: "https://fonts.gstatic.com/s/notosansthai/v20/iJWnBXeUZi_OHPqn4wq6hQ2_hbJ1xyN9wd43SofNWcd1MKVQt_So_9CdU5RtpzF-QRvzzXg.woff2",
      fontWeight: 700,
    }
  ]
});

// Create styles for PDF
const styles = StyleSheet.create({
  page: {
    flexDirection: "column",
    backgroundColor: "#ffffff",
    padding: 30,
    fontFamily: "Helvetica",
  },
  header: {
    fontSize: 24,
    marginBottom: 20,
    textAlign: "center",
    color: "#2563eb",
  },
  section: {
    margin: 10,
    padding: 10,
    flexGrow: 1,
  },
  text: {
    fontSize: 12,
    marginBottom: 10,
    lineHeight: 1.5,
  },
  boldText: {
    fontSize: 14,
    fontWeight: "bold",
    marginBottom: 8,
  },
  table: {
    width: "auto",
    borderStyle: "solid",
    borderWidth: 1,
    borderRightWidth: 0,
    borderBottomWidth: 0,
    marginTop: 20,
  },
  tableRow: {
    margin: "auto",
    flexDirection: "row",
  },
  tableCol: {
    width: "25%",
    borderStyle: "solid",
    borderWidth: 1,
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  tableCell: {
    margin: "auto",
    marginTop: 5,
    fontSize: 10,
    padding: 5,
  },
  footer: {
    position: "absolute",
    fontSize: 10,
    bottom: 30,
    left: 0,
    right: 0,
    textAlign: "center",
    color: "grey",
  },
  thaiText: {
    fontSize: 12,
    marginBottom: 10,
    lineHeight: 1.5,
    fontFamily: "Noto Sans Thai",
  },
  thaiBoldText: {
    fontSize: 14,
    fontWeight: "bold",
    marginBottom: 8,
    fontFamily: "Noto Sans Thai",
  },
});

// Sample data for the PDF
const sampleData = {
  title: "Sample PDF Report",
  titleThai: "รายงานตัวอย่าง PDF",
  date: new Date().toLocaleDateString(),
  items: [
    { id: 1, name: "Product A", nameThai: "สินค้า เอ", price: "$29.99", quantity: 2 },
    { id: 2, name: "Product B", nameThai: "สินค้า บี", price: "$49.99", quantity: 1 },
    { id: 3, name: "Product C", nameThai: "สินค้า ซี", price: "$19.99", quantity: 3 },
    { id: 4, name: "Product D", nameThai: "สินค้า ดี", price: "$39.99", quantity: 1 },
  ],
};

// PDF Document Component
const MyDocument = ({ data }: { data: typeof sampleData }) => (
  <Document>
    <Page size="A4" style={styles.page}>
      <Text style={styles.header}>{data.title}</Text>

      <View style={styles.section}>
        <Text style={styles.boldText}>Report Generated: {data.date}</Text>
        <Text style={styles.text}>
          This is a sample PDF document generated using @react-pdf/renderer in
          an Electron application. This library allows you to create PDF
          documents using React components with a declarative API.
        </Text>

        <Text style={styles.boldText}>Features Demonstrated:</Text>
        <Text style={styles.text}>• Custom styling with StyleSheet</Text>
        <Text style={styles.text}>• Text formatting and layout</Text>
        <Text style={styles.text}>• Table creation</Text>
        <Text style={styles.text}>• Dynamic data rendering</Text>
        <Text style={styles.text}>• PDF download functionality</Text>
        <Text style={styles.text}>• Thai language font support</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.thaiBoldText}>ข้อมูลภาษาไทย: {data.titleThai}</Text>
        <Text style={styles.thaiText}>
          นี่คือตัวอย่างเอกสาร PDF ที่สร้างขึ้นโดยใช้ @react-pdf/renderer ในแอปพลิเคชัน Electron
          ไลบรารีนี้ช่วยให้คุณสามารถสร้างเอกสาร PDF โดยใช้คอมโพเนนต์ React ด้วย API แบบ declarative
        </Text>

        <Text style={styles.thaiBoldText}>คุณสมบัติที่แสดง:</Text>
        <Text style={styles.thaiText}>• การจัดรูปแบบด้วย StyleSheet</Text>
        <Text style={styles.thaiText}>• การจัดรูปแบบข้อความและเลย์เอาต์</Text>
        <Text style={styles.thaiText}>• การสร้างตาราง</Text>
        <Text style={styles.thaiText}>• การแสดงข้อมูลแบบไดนามิก</Text>
        <Text style={styles.thaiText}>• ฟังก์ชันการดาวน์โหลด PDF</Text>
        <Text style={styles.thaiText}>• รองรับฟอนต์ภาษาไทย</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.boldText}>Sample Data Table:</Text>

        <View style={styles.table}>
          {/* Table Header */}
          <View style={styles.tableRow}>
            <View style={styles.tableCol}>
              <Text style={[styles.tableCell, styles.boldText]}>ID</Text>
            </View>
            <View style={styles.tableCol}>
              <Text style={[styles.tableCell, styles.boldText]}>
                Product Name
              </Text>
            </View>
            <View style={styles.tableCol}>
              <Text style={[styles.tableCell, styles.boldText]}>Price</Text>
            </View>
            <View style={styles.tableCol}>
              <Text style={[styles.tableCell, styles.boldText]}>Quantity</Text>
            </View>
          </View>

          {/* Table Rows */}
          {data.items.map((item) => (
            <View style={styles.tableRow} key={item.id}>
              <View style={styles.tableCol}>
                <Text style={styles.tableCell}>{item.id}</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCell}>{item.name}</Text>
                <Text style={[styles.tableCell, styles.thaiText, { fontSize: 8, marginTop: 2 }]}>
                  {item.nameThai}
                </Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCell}>{item.price}</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={styles.tableCell}>{item.quantity}</Text>
              </View>
            </View>
          ))}
        </View>
      </View>

      <Text style={styles.footer}>
        Generated by Electron App with @react-pdf/renderer - Page 1
      </Text>
    </Page>
  </Document>
);

export function ExampleUsageScreen() {
  const [showPreview, setShowPreview] = useState(false);
  const [customTitle, setCustomTitle] = useState(sampleData.title);
  const [customTitleThai, setCustomTitleThai] = useState(sampleData.titleThai);

  const currentData = {
    ...sampleData,
    title: customTitle,
    titleThai: customTitleThai,
    date: new Date().toLocaleDateString(),
  };

  return (
    <div className="flex flex-col items-center gap-6 p-6 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-center mb-4">
        React PDF Renderer Example
      </h1>

      <div className="bg-white rounded-lg shadow-lg p-6 w-full">
        <h2 className="text-xl font-semibold mb-4">PDF Generation Controls</h2>

        <div className="flex flex-col gap-4 mb-6">
          <div>
            <label
              htmlFor="title"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              PDF Title:
            </label>
            <input
              id="title"
              type="text"
              value={customTitle}
              onChange={(e) => setCustomTitle(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter PDF title..."
            />
          </div>

          <div>
            <label
              htmlFor="titleThai"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              PDF Title (Thai):
            </label>
            <input
              id="titleThai"
              type="text"
              value={customTitleThai}
              onChange={(e) => setCustomTitleThai(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="ใส่ชื่อเรื่อง PDF ภาษาไทย..."
              style={{ fontFamily: 'Noto Sans Thai, sans-serif' }}
            />
          </div>

          <div className="flex gap-4">
            <PDFDownloadLink
              document={<MyDocument data={currentData} />}
              fileName={`${customTitle.replace(/\s+/g, "_").toLowerCase()}.pdf`}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors"
            >
              {({ loading }) =>
                loading ? "Generating PDF..." : "Download PDF"
              }
            </PDFDownloadLink>

            <button
              onClick={() => setShowPreview(!showPreview)}
              className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md transition-colors"
            >
              {showPreview ? "Hide Preview" : "Show Preview"}
            </button>
          </div>
        </div>

        {showPreview && (
          <div className="border rounded-lg overflow-hidden">
            <h3 className="text-lg font-semibold p-4 bg-gray-50 border-b">
              PDF Preview
            </h3>
            <div style={{ height: "600px" }}>
              <PDFViewer width="100%" height="100%">
                <MyDocument data={currentData} />
              </PDFViewer>
            </div>
          </div>
        )}
      </div>

      <div className="bg-gray-50 rounded-lg p-6 w-full">
        <h2 className="text-xl font-semibold mb-4">Code Example</h2>
        <pre className="bg-gray-800 text-green-400 p-4 rounded-md overflow-x-auto text-sm">
          {`import { Document, Page, Text, View, StyleSheet, PDFDownloadLink, Font } from '@react-pdf/renderer'

// Register Thai font
Font.register({
  family: "Noto Sans Thai",
  fonts: [
    {
      src: "https://fonts.gstatic.com/s/notosansthai/v20/iJWnBXeUZi_OHPqn4wq6hQ2_hbJ1xyN9wd43SofNWcd1MKVQt_So_9CdU5RtpzF-QRvzzXg.woff2",
      fontWeight: 400,
    }
  ]
});

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#ffffff',
    padding: 30
  },
  section: {
    margin: 10,
    padding: 10,
    flexGrow: 1
  },
  thaiText: {
    fontFamily: "Noto Sans Thai",
    fontSize: 12
  }
})

const MyDocument = () => (
  <Document>
    <Page size="A4" style={styles.page}>
      <View style={styles.section}>
        <Text>Hello World!</Text>
        <Text style={styles.thaiText}>สวัสดีครับ</Text>
      </View>
    </Page>
  </Document>
)

// Usage in component
<PDFDownloadLink document={<MyDocument />} fileName="example.pdf">
  {({ loading }) => (loading ? 'Loading...' : 'Download PDF')}
</PDFDownloadLink>`}
        </pre>
      </div>
    </div>
  );
}
